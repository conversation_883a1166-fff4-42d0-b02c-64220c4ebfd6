//
//  TestAICorrection.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import Foundation
import SwiftUI

// MARK: - AI修正测试视图
struct TestAICorrectionView: View {
    @StateObject private var correctionService = AITextCorrectionService.shared
    @State private var originalText = "下下午好好呀呀呀，下下午好好呀呀呀。"
    @State private var correctedText = ""
    @State private var isLoading = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 标题
                Text("AI文本修正测试")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding()
                
                // 原始文本输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("原始识别文本:")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    TextEditor(text: $originalText)
                        .frame(height: 100)
                        .padding(8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                }
                
                // 修正按钮
                Button(action: {
                    Task {
                        await performCorrection()
                    }
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "wand.and.rays")
                        }
                        Text(isLoading ? "修正中..." : "AI修正")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(isLoading ? Color.gray : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
                }
                .disabled(isLoading || originalText.isEmpty)
                
                // 修正结果
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI修正结果:")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    ScrollView {
                        Text(correctedText.isEmpty ? "等待修正..." : correctedText)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            .foregroundColor(correctedText.isEmpty ? .secondary : .primary)
                    }
                    .frame(height: 100)
                }
                
                // 错误信息
                if !correctionService.errorMessage.isEmpty {
                    Text(correctionService.errorMessage)
                        .foregroundColor(.red)
                        .font(.caption)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer()
                
                // 预设测试用例
                VStack(alignment: .leading, spacing: 8) {
                    Text("预设测试用例:")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            ForEach(testCases, id: \.self) { testCase in
                                Button(testCase) {
                                    originalText = testCase
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                                .font(.caption)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    // MARK: - 执行修正
    private func performCorrection() async {
        isLoading = true
        correctedText = ""
        
        let result = await correctionService.correctText(originalText)
        
        await MainActor.run {
            correctedText = result
            isLoading = false
        }
    }
    
    // MARK: - 测试用例
    private let testCases = [
        "下下午好好呀呀呀，下下午好好呀呀呀。",
        "你你好好，我我是是小小明明。",
        "今今天天天气气很很好好。",
        "我我要要去去北北京京旅旅游游。",
        "请请帮帮我我订订一一张张机机票票。"
    ]
}

// MARK: - 预览
#Preview {
    TestAICorrectionView()
}
