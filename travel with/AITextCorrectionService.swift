//
//  AITextCorrectionService.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import Foundation

// MARK: - AI文本修正服务
@MainActor
class AITextCorrectionService: ObservableObject {
    static let shared = AITextCorrectionService()
    
    // 火山引擎API配置
    private let apiKey = "5bd19fd1-fee3-4290-b8d8-f21fcadeaf51"
    private let apiURL = "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
    private let modelName = "ep-20250801145447-qnsr8"
    
    // 状态管理
    @Published var isProcessing = false
    @Published var errorMessage = ""
    
    private init() {}
    
    // MARK: - 修正文本
    func correctText(_ originalText: String) async -> String {
        guard !originalText.isEmpty else { return originalText }
        
        print("🤖 开始AI修正: \(originalText)")
        
        // 设置处理状态
        isProcessing = true
        errorMessage = ""
        
        do {
            let correctedText = await performTextCorrection(originalText)
            isProcessing = false
            return correctedText
        } catch {
            print("❌ AI修正失败: \(error)")
            errorMessage = "AI修正失败: \(error.localizedDescription)"
            isProcessing = false
            return originalText // 返回原始文本作为备选
        }
    }
    
    // MARK: - 执行文本修正
    private func performTextCorrection(_ text: String) async throws -> String {
        // 构建请求体
        let requestBody: [String: Any] = [
            "model": modelName,
            "messages": [
                [
                    "role": "system",
                    "content": createSystemPrompt()
                ],
                [
                    "role": "user", 
                    "content": text
                ]
            ],
            "temperature": 0.1,  // 低温度确保输出稳定
            "max_tokens": 200    // 限制输出长度
        ]
        
        // 转换为JSON
        guard let jsonData = try? JSONSerialization.data(withJSONObject: requestBody) else {
            throw AITextCorrectionError.jsonSerializationFailed
        }
        
        // 创建请求
        guard let url = URL(string: apiURL) else {
            throw AITextCorrectionError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.httpBody = jsonData
        
        print("🌐 发送AI修正请求...")
        print("📤 原始文本: \(text)")
        
        // 发送请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        // 检查HTTP响应
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AITextCorrectionError.invalidResponse
        }
        
        print("📥 HTTP状态码: \(httpResponse.statusCode)")
        
        guard httpResponse.statusCode == 200 else {
            print("❌ HTTP错误: \(httpResponse.statusCode)")
            if let errorString = String(data: data, encoding: .utf8) {
                print("❌ 错误详情: \(errorString)")
            }
            throw AITextCorrectionError.httpError(httpResponse.statusCode)
        }
        
        // 解析响应
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw AITextCorrectionError.jsonParsingFailed
        }
        
        print("📥 AI响应: \(json)")
        
        // 提取修正后的文本
        guard let choices = json["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let correctedText = message["content"] as? String else {
            throw AITextCorrectionError.responseParsingFailed
        }
        
        let finalText = correctedText.trimmingCharacters(in: .whitespacesAndNewlines)
        print("✅ AI修正完成: \(finalText)")
        
        return finalText
    }
    
    // MARK: - 创建系统提示词
    private func createSystemPrompt() -> String {
        return """
        你是一个专业的语音识别文本修正助手。你的任务是对语音识别模型的输出进行修正，使其更加准确和自然。

        修正规则：
        1. 纠正明显的语音识别错误，如重复词、断句错误等
        2. 修正同音字错误，选择最符合语境的词汇
        3. 补充必要的标点符号，使句子更加通顺
        4. 保持原意不变，只进行必要的修正
        5. 如果原文已经很准确，可以保持不变
        6. 输出应该简洁明了，不要添加多余的解释

        请直接输出修正后的文本，不要包含任何其他内容。
        """
    }
}

// MARK: - 错误类型定义
enum AITextCorrectionError: LocalizedError {
    case invalidURL
    case jsonSerializationFailed
    case invalidResponse
    case httpError(Int)
    case jsonParsingFailed
    case responseParsingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的API URL"
        case .jsonSerializationFailed:
            return "JSON序列化失败"
        case .invalidResponse:
            return "无效的响应"
        case .httpError(let code):
            return "HTTP错误: \(code)"
        case .jsonParsingFailed:
            return "JSON解析失败"
        case .responseParsingFailed:
            return "响应解析失败"
        }
    }
}
