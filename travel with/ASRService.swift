//
//  ASRService.swift
//  travel with
//
//  Created by AI Assistant on 2025/7/31.
//

import Foundation
import AVFoundation
import SwiftUI
import CryptoKit
import Compression

// MARK: - ASR服务管理器
@MainActor
class ASRService: NSObject, ObservableObject {
    // 火山引擎ASR配置
    private let appKey = "1771796339"
    private let accessKey = "MduxvCjM28XnWnXS_a2_NAedHCJ9649D"
    private let secretKey = "fV6rw3JgtauLQ9mOy_p6u2rX3PlmerAt"
    private let asrURL = "wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream"  // 流式输入模式
    private let resourceId = "volc.bigasr.sauc.duration"

    // 录音相关
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var audioFormat: AVAudioFormat?

    // WebSocket连接
    private var webSocketTask: URLSessionWebSocketTask?
    private var urlSession: URLSession?

    // 状态管理
    @Published var isRecording = false
    @Published var isConnected = false
    @Published var recognizedText = ""
    @Published var finalText = ""
    @Published var errorMessage = ""

    // 回调
    var onFinalResult: ((String) -> Void)?
    var onPartialResult: ((String) -> Void)?
    var onError: ((String) -> Void)?

    // 音频缓冲管理
    private var audioBuffer = Data()
    private var lastSendTime = Date()
    private let sendInterval: TimeInterval = 0.20 // 200ms发送一次（流式输入模式推荐值）
    private var sequenceNumber = 1 // 序列号管理（流式输入模式使用正序列号）
    
    override init() {
        super.init()
        setupAudioSession()
        setupAudioEngine()
    }

    // MARK: - GZIP压缩和解压缩
    private func gzipCompress(data: Data) -> Data? {
        return data.withUnsafeBytes { bytes in
            // 分配足够大的缓冲区
            let bufferSize = data.count + 32  // 添加一些额外空间
            let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: bufferSize)
            defer { buffer.deallocate() }

            let compressedSize = compression_encode_buffer(
                buffer, bufferSize,
                bytes.bindMemory(to: UInt8.self).baseAddress!, data.count,
                nil, COMPRESSION_ZLIB
            )

            guard compressedSize > 0 else {
                print("❌ GZIP压缩失败: compression_encode_buffer返回\(compressedSize)")
                return nil
            }

            let result = Data(bytes: buffer, count: compressedSize)
            print("✅ GZIP压缩成功: \(data.count) -> \(compressedSize) 字节")
            return result
        }
    }

    private func gzipDecompress(data: Data) -> Data? {
        return data.withUnsafeBytes { bytes in
            // 分配更大的缓冲区用于解压
            let bufferSize = data.count * 8  // 增加缓冲区大小
            let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: bufferSize)
            defer { buffer.deallocate() }

            let decompressedSize = compression_decode_buffer(
                buffer, bufferSize,
                bytes.bindMemory(to: UInt8.self).baseAddress!, data.count,
                nil, COMPRESSION_ZLIB
            )

            guard decompressedSize > 0 else {
                print("❌ GZIP解压失败: compression_decode_buffer返回\(decompressedSize)")
                return nil
            }

            return Data(bytes: buffer, count: decompressedSize)
        }
    }
    
    deinit {
        Task { @MainActor in
            stopRecording()
            disconnect()
        }
    }
    
    // MARK: - 音频会话设置
    private func setupAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
            print("✅ 音频会话设置成功")
        } catch {
            print("❌ 音频会话设置失败: \(error)")
            errorMessage = "音频会话设置失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 音频引擎设置
    private func setupAudioEngine() {
        audioEngine = AVAudioEngine()
        guard let audioEngine = audioEngine else { return }

        inputNode = audioEngine.inputNode

        // 创建符合ASR要求的音频格式：16kHz, 16位, 单声道
        audioFormat = AVAudioFormat(
            commonFormat: .pcmFormatFloat32,
            sampleRate: 16000.0,
            channels: 1,
            interleaved: false
        )

        guard audioFormat != nil else {
            print("❌ 无法创建音频格式")
            errorMessage = "音频格式创建失败"
            return
        }

        print("✅ 音频引擎设置完成")
        print("🎵 音频格式: \(audioFormat!.description)")
    }


    
    // MARK: - 连接ASR服务
    func connect() async {
        guard !isConnected else { return }

        // 构建WebSocket URL
        guard let url = URL(string: asrURL) else {
            errorMessage = "URL构建失败"
            return
        }

        // 创建请求并添加必要的HTTP头部
        var request = URLRequest(url: url)
        let connectId = UUID().uuidString

        request.setValue(appKey, forHTTPHeaderField: "X-Api-App-Key")
        request.setValue(accessKey, forHTTPHeaderField: "X-Api-Access-Key")
        request.setValue(resourceId, forHTTPHeaderField: "X-Api-Resource-Id")
        request.setValue(connectId, forHTTPHeaderField: "X-Api-Connect-Id")

        print("🔗 连接火山引擎ASR (流式输入模式): \(asrURL)")
        print("🔐 X-Api-App-Key: \(appKey)")
        print("🔐 X-Api-Access-Key: \(accessKey)")
        print("🔐 X-Api-Resource-Id: \(resourceId)")
        print("🔐 X-Api-Connect-Id: \(connectId)")
        print("📊 模式说明: 流式输入模式 - 200ms音频包，音频大于15s或发送最后一包后返回结果，准确率更高")

        // 创建WebSocket连接
        urlSession = URLSession(configuration: .default)
        webSocketTask = urlSession?.webSocketTask(with: request)

        // 开始连接
        webSocketTask?.resume()

        // 监听消息
        listenForMessages()

        // 等待连接建立并发送开始消息
        Task {
            // 等待连接建立
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

            // 发送开始识别消息
            await sendStartMessage()

            await MainActor.run {
                isConnected = true
                print("✅ ASR服务连接完成")
            }
        }
    }
    
    // MARK: - 断开连接
    func disconnect() {
        webSocketTask?.cancel(with: .goingAway, reason: nil)
        webSocketTask = nil
        urlSession = nil
        isConnected = false
        print("🔌 ASR服务已断开")
    }
    
    // MARK: - 生成Authorization头部
    private func generateAuthorizationHeader() -> String {
        // 尝试更简单的Bearer token认证
        return "Bearer \(accessKey)"
    }

    // MARK: - 生成Authorization头部 (HMAC方式备用)
    private func generateHMACAuthorizationHeader() -> String {
        let timestamp = String(Int(Date().timeIntervalSince1970))
        let mac = generateHMACSignature(timestamp: timestamp)
        return "HMAC256; access_token=\"\(accessKey)\"; mac=\"\(mac)\"; timestamp=\"\(timestamp)\""
    }

    // MARK: - 生成HMAC-SHA256签名
    private func generateHMACSignature(timestamp: String) -> String {
        // 根据火山引擎文档，签名字符串格式可能不同
        // 尝试简化的签名格式
        let signString = "\(appKey)\(timestamp)"

        print("🔐 签名字符串: \(signString)")

        // 使用HMAC-SHA256生成签名
        let key = SymmetricKey(data: secretKey.data(using: .utf8)!)
        let signature = HMAC<SHA256>.authenticationCode(for: signString.data(using: .utf8)!, using: key)
        let base64Signature = Data(signature).base64EncodedString()

        print("🔐 生成的签名: \(base64Signature)")

        return base64Signature
    }
    
    // MARK: - 发送开始识别消息
    private func sendStartMessage() async {
        print("🔄 === ASR协议流程开始 ===")
        print("📤 步骤1: client 发送 'Full client request'")

        // 根据Python示例，需要发送二进制协议的完整客户端请求
        let fullClientRequest = createFullClientRequest()

        print("📤 完整客户端请求详情:")
        print("   - 消息类型: Full client request (0x01)")
        print("   - 序列化: JSON")
        print("   - 压缩: NONE (无压缩)")
        print("   - 序列号管理: 流式输入模式使用正序列号")
        print("   - 数据大小: \(fullClientRequest.count) 字节")

        await sendBinaryMessage(fullClientRequest)
        print("✅ 步骤1完成: Full client request 已发送")
    }

    // MARK: - 创建完整客户端请求
    private func createFullClientRequest() -> Data {
        // 构建请求payload (根据官方文档格式)
        let payload: [String: Any] = [
            "user": [
                "uid": "demo_uid"
            ],
            "audio": [
                "format": "wav",
                "codec": "raw",
                "rate": 16000,
                "bits": 16,
                "channel": 1
            ],
            "request": [
                "model_name": "bigmodel",
                "enable_itn": true,          // 逆文本标准化：将数字等转换为文字形式
                "enable_punc": true,         // 启用标点符号
                "enable_ddc": true,          // 启用语义顺滑：删除重复词、停顿词等
                "show_utterances": false,    // 关闭utterances以简化输出
                "enable_nonstream": false,   // 流式输入模式
                "language": "zh-CN",         // 明确指定中文
                "sample_rate": 16000         // 明确指定采样率
            ]
        ]

        print("📤 JSON Payload内容:")
        if let jsonString = String(data: try! JSONSerialization.data(withJSONObject: payload), encoding: .utf8) {
            print("   \(jsonString)")
        }
        print("📤 配置说明:")
        print("   - enable_ddc: true (语义顺滑，删除重复词、停顿词)")
        print("   - show_utterances: false (简化输出，可能提高DDC效果)")
        print("   - language: zh-CN (明确指定中文识别)")

        // 转换为JSON并压缩
        guard let jsonData = try? JSONSerialization.data(withJSONObject: payload) else {
            return Data()
        }

        // 使用无压缩模式，避免压缩相关错误
        print("📤 使用无压缩模式发送JSON数据")
        print("   原始JSON大小: \(jsonData.count) 字节")
        return createFullClientRequestUncompressed(jsonData: jsonData)

        /*
        // 以下是压缩版本的代码，暂时注释掉
        // 如果需要启用压缩，请取消注释并确保正确实现gzip压缩

        // 压缩JSON数据
        guard let compressedData = try? (jsonData as NSData).compressed(using: .zlib) as Data else {
            print("❌ JSON数据压缩失败，使用无压缩模式")
            return createFullClientRequestUncompressed(jsonData: jsonData)
        }

        // 构建二进制请求头部和消息
        var request = Data()

        // 协议版本和消息类型 (根据官方文档)
        request.append(0x11) // Protocol version 1, header size 1
        request.append(0x10) // Message type: CLIENT_FULL_REQUEST(0001), flags: 无序列号(0000)
        request.append(0x11) // Serialization: JSON(0001), Compression: GZIP(0001)
        request.append(0x00) // Reserved

        // Payload大小 (4字节，大端序)
        let payloadSize = UInt32(compressedData.count)
        request.append(contentsOf: withUnsafeBytes(of: payloadSize.bigEndian) { Array($0) })

        // Payload数据
        request.append(compressedData)

        print("📤 完整客户端请求(压缩) - 原始大小: \(jsonData.count) 字节, 压缩后: \(compressedData.count) 字节")

        return request
        */
    }

    // MARK: - 创建未压缩的完整客户端请求（流式输入模式）
    private func createFullClientRequestUncompressed(jsonData: Data) -> Data {
        var request = Data()

        // 协议版本和消息类型 (流式输入模式需要序列号)
        request.append(0x11) // Protocol version 1, header size 1
        request.append(0x11) // Message type: CLIENT_FULL_REQUEST(0001), flags: 正序列号(0001)
        request.append(0x10) // Serialization: JSON(0001), Compression: NONE(0000)
        request.append(0x00) // Reserved

        // 添加序列号 (4字节，大端序)
        let currentSeq = Int32(sequenceNumber)
        request.append(contentsOf: withUnsafeBytes(of: currentSeq.bigEndian) { Array($0) })
        sequenceNumber += 1 // 递增序列号

        // Payload大小 (4字节，大端序)
        let payloadSize = UInt32(jsonData.count)
        request.append(contentsOf: withUnsafeBytes(of: payloadSize.bigEndian) { Array($0) })

        // Payload数据
        request.append(jsonData)

        print("📤 完整客户端请求(流式输入模式) - 序列号: \(currentSeq), 大小: \(jsonData.count) 字节")

        return request
    }
    
    // MARK: - 发送二进制消息
    private func sendBinaryMessage(_ data: Data) async {
        guard let webSocketTask = webSocketTask else {
            print("❌ WebSocket任务为空")
            return
        }

        // 检查连接状态
        print("📤 WebSocket状态: \(webSocketTask.state)")
        print("📤 发送二进制数据: \(data.count) 字节")

        do {
            let wsMessage = URLSessionWebSocketTask.Message.data(data)
            try await webSocketTask.send(wsMessage)
            print("✅ 二进制消息发送成功")
        } catch {
            print("❌ 发送二进制消息失败: \(error)")
            print("❌ WebSocket状态: \(webSocketTask.state)")
            errorMessage = "发送消息失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 监听消息
    private func listenForMessages() {
        guard let webSocketTask = webSocketTask else { return }

        webSocketTask.receive { [weak self] result in
            switch result {
            case .success(let message):
                Task { @MainActor in
                    await self?.handleMessage(message)
                    self?.listenForMessages() // 继续监听
                }
            case .failure(let error):
                Task { @MainActor in
                    print("❌ WebSocket接收消息失败: \(error)")
                    self?.errorMessage = "接收消息失败: \(error.localizedDescription)"
                    self?.onError?("接收消息失败: \(error.localizedDescription)")

                    // 如果正在录音，尝试重连
                    if let self = self, self.isRecording {
                        await self.handleConnectionLoss()
                    }
                }
            }
        }
    }

    // MARK: - 处理连接丢失
    private func handleConnectionLoss() async {
        print("🔄 检测到连接丢失，准备重连...")

        // 断开当前连接
        disconnect()

        // 等待一段时间
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        // 如果仍在录音，尝试重连
        if isRecording {
            print("🔄 重新连接ASR服务...")
            await connect()
        }
    }
    
    // MARK: - 处理接收到的消息
    private func handleMessage(_ message: URLSessionWebSocketTask.Message) async {
        switch message {
        case .data(let data):
            print("📥 收到服务器响应: \(data.count) 字节")
            print("📊 流式输入模式: 音频大于15s或发送最后一包后返回识别结果")
            // 尝试将数据转换为字符串查看内容
            if let dataString = String(data: data, encoding: .utf8) {
                print("📥 数据内容: \(dataString)")
            } else {
                print("📥 数据内容: 二进制数据，前20字节: \(data.prefix(20).map { String(format: "%02x", $0) }.joined(separator: " "))")
            }
            await parseASRResponse(data)
        case .string(let text):
            print("📥 收到文本消息: \(text)")
            print("📊 流式输入模式: 音频大于15s或发送最后一包后返回识别结果")
            if let data = text.data(using: .utf8) {
                await parseASRResponse(data)
            }
        @unknown default:
            print("📥 收到未知类型消息")
            break
        }
    }
    
    // MARK: - 解析ASR响应
    private func parseASRResponse(_ data: Data) async {
        // 解析二进制协议响应
        guard data.count >= 4 else {
            print("❌ 响应数据太短")
            return
        }

        // 解析协议头部
        let headerSize = Int(data[0] & 0x0f)
        let messageType = (data[1] >> 4) & 0x0f
        let messageTypeFlags = data[1] & 0x0f
        let serializationType = (data[2] >> 4) & 0x0f
        let compressionType = data[2] & 0x0f

        // 识别响应类型和步骤
        let responseTypeStr: String
        let stepStr: String

        if messageType == 0x09 { // SERVER_FULL_RESPONSE
            responseTypeStr = "Full server response"
            if !isRecording {
                stepStr = "步骤2: server 响应 'Full server response' (对Full client request的响应)"
            } else {
                stepStr = "步骤4: server 响应 'Full server response' (对Audio request的响应)"
            }
        } else if messageType == 0x0f { // SERVER_ERROR_RESPONSE
            responseTypeStr = "Error response"
            stepStr = "❌ 错误响应"
        } else {
            responseTypeStr = "Unknown response"
            stepStr = "❓ 未知响应"
        }

        print("📥 \(stepStr)")
        print("📥 响应详情:")
        print("   - 消息类型: \(responseTypeStr) (0x\(String(format: "%02x", messageType)))")
        print("   - 头部大小: \(headerSize), 标志: \(messageTypeFlags)")
        print("   - 序列化: \(serializationType), 压缩: \(compressionType)")

        // 检查是否是错误响应
        if messageType == 0x0f { // SERVER_ERROR_RESPONSE
            print("❌ 收到服务器错误响应")
            // 尝试解析错误信息
            await handleErrorResponse(data)
            return
        }

        // 跳过协议头部，获取payload
        let headerBytes = headerSize * 4
        guard data.count > headerBytes else {
            print("❌ 数据长度不足")
            return
        }

        var payload = data.subdata(in: headerBytes..<data.count)

        // 如果有序列号，跳过4字节
        if (messageTypeFlags & 0x01) != 0 {
            guard payload.count >= 4 else { return }
            let sequence = payload.withUnsafeBytes { $0.load(fromByteOffset: 0, as: Int32.self).bigEndian }
            print("📥 序列号: \(sequence)")
            payload = payload.subdata(in: 4..<payload.count)
        }

        // 如果有事件标志，跳过4字节
        if (messageTypeFlags & 0x04) != 0 {
            guard payload.count >= 4 else { return }
            payload = payload.subdata(in: 4..<payload.count)
        }

        // 如果是完整响应，读取payload大小
        if messageType == 0x09 { // SERVER_FULL_RESPONSE
            guard payload.count >= 4 else { return }
            let payloadSize = payload.withUnsafeBytes { $0.load(fromByteOffset: 0, as: UInt32.self).bigEndian }
            print("📥 Payload大小: \(payloadSize)")

            // 验证payload大小是否合理
            if payloadSize > 1024 * 1024 { // 1MB限制
                print("❌ Payload大小异常: \(payloadSize)")
                return
            }

            payload = payload.subdata(in: 4..<payload.count)
        }

        // 解压缩payload (如果需要)
        if compressionType == 0x01 { // GZIP压缩
            if let decompressed = gzipDecompress(data: payload) {
                payload = decompressed
                print("📥 GZIP解压缩成功，解压后大小: \(payload.count)")
            } else {
                print("❌ GZIP解压缩失败")
                return
            }
        }

        // 解析JSON payload
        if payload.count > 0 {
            do {
                let json = try JSONSerialization.jsonObject(with: payload, options: [])
                if let dict = json as? [String: Any] {
                    print("📥 ASR响应: \(dict)")

                    // 检查错误
                    if let error = dict["error"] as? [String: Any] {
                        let errorCode = error["code"] as? Int ?? 0
                        let errorMessage = error["message"] as? String ?? "未知错误"
                        print("❌ 服务器错误: \(errorCode) - \(errorMessage)")
                        self.errorMessage = "服务器错误: \(errorMessage)"
                        onError?("服务器错误: \(errorMessage)")
                        return
                    }

                    // 处理识别结果
                    if let result = dict["result"] as? [String: Any],
                       let text = result["text"] as? String {
                        if !text.isEmpty {
                            recognizedText = text
                            onPartialResult?(text)
                            print("🎤 识别结果: \(text)")
                        }
                    }

                    if let isFinal = dict["is_final"] as? Bool, isFinal {
                        finalText = recognizedText
                        onFinalResult?(recognizedText)
                        print("✅ 最终结果: \(recognizedText)")
                    }
                }

                // 根据当前状态打印步骤完成信息
                if !isRecording {
                    print("✅ 步骤2完成: Full server response 已接收")
                } else {
                    print("✅ 步骤4完成: Full server response 已接收")
                }

            } catch {
                print("❌ 解析JSON失败: \(error)")
                // 尝试直接输出payload内容
                if let payloadString = String(data: payload, encoding: .utf8) {
                    print("📥 Payload内容: \(payloadString)")
                }
            }
        }
    }

    // MARK: - 处理错误响应
    private func handleErrorResponse(_ data: Data) async {
        print("❌ === 解析错误响应 ===")

        // 根据文档：Error message from server 格式
        // Header (4字节) + Error code (4字节) + Error message size (4字节) + Error message
        guard data.count >= 12 else {
            print("❌ 错误响应数据太短: \(data.count) 字节")
            return
        }

        // 跳过header (4字节)
        let errorCodeData = data.subdata(in: 4..<8)
        let errorSizeData = data.subdata(in: 8..<12)

        // 解析错误码 (大端序)
        let errorCode = errorCodeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }
        let errorSize = errorSizeData.withUnsafeBytes { $0.load(as: UInt32.self).bigEndian }

        print("❌ 错误码: \(errorCode)")
        print("❌ 错误信息大小: \(errorSize)")

        // 解析错误信息
        if data.count >= 12 + Int(errorSize) {
            let errorMessageData = data.subdata(in: 12..<(12 + Int(errorSize)))
            if let errorMessage = String(data: errorMessageData, encoding: .utf8) {
                print("❌ 错误信息: \(errorMessage)")

                // 根据错误码提供具体说明
                let errorDescription = getErrorDescription(errorCode: errorCode)
                print("❌ 错误说明: \(errorDescription)")

                self.errorMessage = "服务器错误(\(errorCode)): \(errorMessage)"
                onError?("服务器错误(\(errorCode)): \(errorMessage)")
            }
        }

        print("❌ === 错误响应解析完成 ===")

        // 断开连接
        disconnect()
    }

    // MARK: - 获取错误码描述
    private func getErrorDescription(errorCode: UInt32) -> String {
        switch errorCode {
        case 20000000:
            return "成功"
        case 45000001:
            return "请求参数无效 - 请求参数缺失必需字段/字段值无效/重复请求"
        case 45000002:
            return "空音频"
        case 45000081:
            return "等包超时"
        case 45000151:
            return "音频格式不正确"
        case 55000031:
            return "服务器繁忙 - 服务过载，无法处理当前请求"
        default:
            if errorCode >= 550_00000 && errorCode <= 550_99999 {
                return "服务内部处理错误"
            }
            return "未知错误码"
        }
    }
    
    // MARK: - 开始录音
    func startRecording() async {
        guard !isRecording else { return }

        // 请求麦克风权限
        let permission = await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
        guard permission else {
            errorMessage = "需要麦克风权限才能进行语音识别"
            onError?("需要麦克风权限才能进行语音识别")
            return
        }

        // 清空之前的结果和重置序列号
        recognizedText = ""
        finalText = ""
        errorMessage = ""
        sequenceNumber = 1 // 重置序列号
        
        // 完全清空音频缓冲区，防止任何残留数据
        audioBuffer.removeAll()
        print("🔄 完全重置音频缓冲区，确保无残留数据")
        
        // 如果连接已断开，先断开并重新连接
        if webSocketTask?.state != .running {
            disconnect()
        }

        // 使用火山引擎ASR
        await startVolcanoASR()
    }



    // MARK: - 火山引擎ASR
    private func startVolcanoASR() async {
        // 连接ASR服务
        if !isConnected {
            await connect()

            // 等待连接完全建立
            var waitCount = 0
            while !isConnected && waitCount < 10 {
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                waitCount += 1
            }

            if !isConnected {
                errorMessage = "ASR服务连接超时"
                onError?("ASR服务连接超时")
                return
            }
        }

        guard let audioEngine = audioEngine,
              let inputNode = inputNode,
              let targetFormat = audioFormat else {
            errorMessage = "音频引擎未初始化"
            return
        }

        // 获取输入节点的原始格式
        let inputFormat = inputNode.outputFormat(forBus: 0)
        print("🎵 输入格式: \(inputFormat.description)")
        print("🎵 目标格式: \(targetFormat.description)")

        // 创建格式转换器
        guard let converter = AVAudioConverter(from: inputFormat, to: targetFormat) else {
            errorMessage = "无法创建音频格式转换器"
            print("❌ 音频格式转换器创建失败")
            return
        }

        // 清空音频缓冲区并重置时间戳
        audioBuffer.removeAll()
        lastSendTime = Date()
        print("🔄 音频缓冲区已清空，防止重叠")

        // 安装音频tap，使用较大的缓冲区减少回调频率
        // 使用4096样本，在48kHz下约85ms，配合时间控制实现200ms发送间隔
        inputNode.installTap(onBus: 0, bufferSize: 4096, format: inputFormat) { [weak self] buffer, _ in
            Task {
                await self?.processAndSendAudioData(buffer, converter: converter, targetFormat: targetFormat)
            }
        }

        // 启动音频引擎
        do {
            try audioEngine.start()
            isRecording = true
            print("🎤 开始火山引擎ASR")
        } catch {
            print("❌ 启动录音失败: \(error)")
            errorMessage = "启动录音失败: \(error.localizedDescription)"
            onError?("启动录音失败: \(error.localizedDescription)")
        }
    }
    
    // MARK: - 停止录音
    func stopRecording() {
        guard isRecording else { return }

        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        isRecording = false

        // 发送剩余缓冲数据和结束消息
        Task {
            await sendRemainingBufferAndEnd()
        }
        print("🛑 停止火山引擎ASR")
    }

    // MARK: - 发送剩余缓冲数据并结束
    private func sendRemainingBufferAndEnd() async {
        // 计算200ms对应的字节数
        let targetBytesFor200ms = Int(Double(16000) * 0.2) * 2 * 1  // 6400字节

        print("📤 处理剩余缓冲数据: \(audioBuffer.count) 字节")

        // 发送所有完整的200ms包
        while audioBuffer.count >= targetBytesFor200ms {
            let dataToSend = Data(audioBuffer.prefix(targetBytesFor200ms))
            let wavData = createWAVData(pcmData: dataToSend)
            print("📤 发送剩余完整音频包: \(wavData.count) 字节")
            let audioRequest = createAudioOnlyRequest(audioData: wavData, isLast: false)
            await sendBinaryMessage(audioRequest)
            
            // 精确移除已发送的数据
            audioBuffer.removeFirst(targetBytesFor200ms)
        }

        // 如果还有不足200ms的数据，也发送
        if audioBuffer.count > 0 {
            let wavData = createWAVData(pcmData: audioBuffer)
            print("📤 发送剩余不完整音频数据: \(wavData.count) 字节 (原始: \(audioBuffer.count) 字节)")
            let audioRequest = createAudioOnlyRequest(audioData: wavData, isLast: false)
            await sendBinaryMessage(audioRequest)
            audioBuffer.removeAll()
        }

        // 发送结束消息
        print("📤 步骤5: client 发送包含最后一包音频数据的 'Audio-only client request'")
        await sendEndMessage()
        print("✅ 步骤5完成: 最后的Audio-only request 已发送")
        print("🔄 === ASR协议流程结束 ===")
    }
    
    // MARK: - 处理并发送音频数据（带格式转换）
    private func processAndSendAudioData(_ buffer: AVAudioPCMBuffer, converter: AVAudioConverter, targetFormat: AVAudioFormat) async {
        guard let webSocketTask = webSocketTask else {
            print("❌ WebSocket任务为空")
            return
        }

        // 检查连接状态
        if webSocketTask.state != .running {
            print("❌ WebSocket连接状态异常: \(webSocketTask.state)")
            return
        }

        // 创建目标格式的缓冲区
        let frameCapacity = buffer.frameLength
        guard let convertedBuffer = AVAudioPCMBuffer(pcmFormat: targetFormat, frameCapacity: frameCapacity) else {
            print("❌ 无法创建转换后的音频缓冲区")
            return
        }

        // 执行格式转换
        var error: NSError?
        let inputBlock: AVAudioConverterInputBlock = { inNumPackets, outStatus in
            outStatus.pointee = .haveData
            return buffer
        }

        let status = converter.convert(to: convertedBuffer, error: &error, withInputFrom: inputBlock)

        if status == .error {
            print("❌ 音频格式转换失败: \(error?.localizedDescription ?? "未知错误")")
            return
        }

        // 发送转换后的音频数据
        await sendAudioData(convertedBuffer)
    }

    // MARK: - 发送音频数据
    private func sendAudioData(_ buffer: AVAudioPCMBuffer) async {
        guard let webSocketTask = webSocketTask,
              let channelData = buffer.floatChannelData?[0] else {
            return
        }

        // 检查连接状态
        if webSocketTask.state != .running {
            print("❌ WebSocket连接状态异常: \(webSocketTask.state)")
            return
        }

        let frameLength = Int(buffer.frameLength)

        // 将Float32数据转换为16位PCM格式
        var pcmData = Data()
        for i in 0..<frameLength {
            let sample = channelData[i]
            // 将Float32 (-1.0 to 1.0) 转换为Int16 (-32768 to 32767)
            let pcmSample = Int16(sample * 32767.0)
            withUnsafeBytes(of: pcmSample.littleEndian) { bytes in
                pcmData.append(contentsOf: bytes)
            }
        }

        // 添加到缓冲区
        audioBuffer.append(pcmData)

        // 检查是否到了发送时间（200ms间隔控制）
        let currentTime = Date()
        let timeSinceLastSend = currentTime.timeIntervalSince(lastSendTime)

        // 计算200ms对应的字节数 (16kHz, 16位, 单声道)
        let sampleRate = 16000
        let bytesPerSample = 2  // 16位 = 2字节
        let channels = 1
        let targetBytesFor200ms = Int(Double(sampleRate) * 0.2) * bytesPerSample * channels  // 6400字节

        // 只有当时间间隔足够或缓冲区数据足够多时才发送
        let shouldSendByTime = timeSinceLastSend >= sendInterval
        let shouldSendByBuffer = audioBuffer.count >= targetBytesFor200ms * 2  // 缓冲区有400ms数据时强制发送

        guard shouldSendByTime || shouldSendByBuffer else {
            return  // 还没到发送时间，继续累积数据
        }

        // 更新发送时间
        lastSendTime = currentTime

        // 只处理完整的200ms包，避免重复发送
        var packetsProcessed = 0
        while audioBuffer.count >= targetBytesFor200ms && packetsProcessed < 2 {  // 限制每次最多处理2个包
            // 创建新的数据副本，确保数据完整性
            let dataToSend = Data(audioBuffer.prefix(targetBytesFor200ms))
            let wavData = createWAVData(pcmData: dataToSend)

            print("📤 步骤3: client 发送包含音频数据的 'Audio only client request'")
            print("📤 音频数据详情:")
            print("   - 消息类型: Audio only client request (0x02)")
            print("   - 标志: 正常包，使用正序列号")
            print("   - 音频包大小: 精确200ms (\(targetBytesFor200ms) 字节)")
            print("   - WAV格式大小: \(wavData.count) 字节")
            print("   - 缓冲区总大小: \(audioBuffer.count) 字节")
            print("   - 包序号: \(packetsProcessed + 1)")
            print("   - 时间间隔: \(String(format: "%.3f", timeSinceLastSend))s")

            // 使用二进制协议发送音频数据
            let audioRequest = createAudioOnlyRequest(audioData: wavData, isLast: false)
            await sendBinaryMessage(audioRequest)

            // 精确移除已发送的数据
            audioBuffer.removeFirst(targetBytesFor200ms)
            packetsProcessed += 1
            
            print("✅ 步骤3完成: Audio only request 已发送，剩余缓冲区: \(audioBuffer.count) 字节")
        }

        // 防止缓冲区过度累积
        let maxBufferSize = targetBytesFor200ms * 3  // 最多累积3个包的数据（600ms）
        if audioBuffer.count > maxBufferSize {
            print("⚠️ 缓冲区过大，丢弃旧数据: \(audioBuffer.count) -> \(targetBytesFor200ms)")
            // 只保留最新的一个完整包的数据
            audioBuffer = audioBuffer.suffix(targetBytesFor200ms)
        }
    }

    // MARK: - 创建WAV格式数据
    private func createWAVData(pcmData: Data) -> Data {
        var wavData = Data()

        // WAV文件头部
        let sampleRate: UInt32 = 16000
        let channels: UInt16 = 1
        let bitsPerSample: UInt16 = 16
        let bytesPerSample = bitsPerSample / 8
        let blockAlign = channels * bytesPerSample
        let byteRate = sampleRate * UInt32(blockAlign)
        let dataSize = UInt32(pcmData.count)
        let fileSize = 36 + dataSize

        // RIFF头部
        wavData.append("RIFF".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: fileSize.littleEndian) { Data($0) })
        wavData.append("WAVE".data(using: .ascii)!)

        // fmt子块
        wavData.append("fmt ".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: UInt32(16).littleEndian) { Data($0) }) // fmt子块大小
        wavData.append(withUnsafeBytes(of: UInt16(1).littleEndian) { Data($0) })  // 音频格式 (PCM)
        wavData.append(withUnsafeBytes(of: channels.littleEndian) { Data($0) })   // 声道数
        wavData.append(withUnsafeBytes(of: sampleRate.littleEndian) { Data($0) }) // 采样率
        wavData.append(withUnsafeBytes(of: byteRate.littleEndian) { Data($0) })   // 字节率
        wavData.append(withUnsafeBytes(of: blockAlign.littleEndian) { Data($0) }) // 块对齐
        wavData.append(withUnsafeBytes(of: bitsPerSample.littleEndian) { Data($0) }) // 位深度

        // data子块
        wavData.append("data".data(using: .ascii)!)
        wavData.append(withUnsafeBytes(of: dataSize.littleEndian) { Data($0) })
        wavData.append(pcmData)

        return wavData
    }
    
    // MARK: - 发送结束消息
    private func sendEndMessage() async {
        print("📤 最后包详情:")
        print("   - 消息类型: Audio only client request (0x02)")
        print("   - 标志: 负序列号 - 表示最后一包")
        print("   - 数据大小: 0 字节 (空数据包)")
        print("   - 模式: 流式输入模式 - 结束音频流，等待最终识别结果")

        // 根据Python示例，发送最后一个音频包来表示结束
        // 这里发送一个空的音频数据包，标记为最后一个
        let endRequest = createAudioOnlyRequest(audioData: Data(), isLast: true)
        await sendBinaryMessage(endRequest)
    }

    // MARK: - 创建仅音频请求（流式输入模式）
    private func createAudioOnlyRequest(audioData: Data, isLast: Bool = false) -> Data {
        var request = Data()

        // 协议头部 (流式输入模式)
        request.append(0x11) // Protocol version 1, header size 1
        
        if isLast {
            // 最后一包使用负序列号
            request.append(0x23) // Message type: AUDIO_ONLY(0010), flags: 负序列号+序列号(0011)
            print("📤 构建最后一包 - flags: 0011 (负序列号+序列号)")
        } else {
            // 正常包使用正序列号
            request.append(0x21) // Message type: AUDIO_ONLY(0010), flags: 正序列号(0001)
            print("📤 构建正常音频包 - flags: 0001 (正序列号)")
        }
        
        request.append(0x00) // Serialization: NONE(0000), Compression: NONE(0000)
        request.append(0x00) // Reserved

        // 添加序列号 (流式输入模式必需)
        let currentSeq = isLast ? -Int32(sequenceNumber) : Int32(sequenceNumber)
        request.append(contentsOf: withUnsafeBytes(of: currentSeq.bigEndian) { Array($0) })
        
        if !isLast {
            sequenceNumber += 1 // 正常包递增序列号，最后包不递增
        }

        // Payload大小
        let payloadSize = UInt32(audioData.count)
        request.append(contentsOf: withUnsafeBytes(of: payloadSize.bigEndian) { Array($0) })

        // Payload数据
        request.append(audioData)

        print("📤 音频请求构建完成:")
        print("   - 是否最后包: \(isLast)")
        print("   - 序列号: \(currentSeq)")
        print("   - 音频数据大小: \(audioData.count) 字节")
        print("   - 压缩模式: 无压缩")
        print("   - 总请求大小: \(request.count) 字节")

        return request
    }
}



// MARK: - String扩展：SHA256
extension String {
    func sha256() -> String {
        let data = Data(self.utf8)
        let digest = SHA256.hash(data: data)
        return digest.compactMap { String(format: "%02x", $0) }.joined()
    }
}
