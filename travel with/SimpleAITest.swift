//
//  SimpleAITest.swift
//  travel with
//
//  Created by AI Assistant on 2025/8/1.
//

import Foundation

// MARK: - 简单AI修正测试
class SimpleAITest {
    
    static func testCorrection() async {
        print("🚀 测试AI文本修正")
        print("=" * 40)
        
        // 测试文本
        let originalText = "下下午好好呀呀呀，下下午好好呀呀呀。"
        print("📝 原始文本: \(originalText)")
        
        // 执行修正
        let correctedText = await AITextCorrectionService.shared.correctText(originalText)
        print("✅ 修正结果: \(correctedText)")
        
        print("=" * 40)
        print("🎯 测试完成")
    }
}

// MARK: - 字符串扩展
extension String {
    static func * (left: String, right: Int) -> String {
        return String(repeating: left, count: right)
    }
}
